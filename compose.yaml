﻿services:
  postgres:
    image: postgres:latest
    environment:
      POSTGRES_DB: ejbca_license
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5433:5432"
    volumes:
      - ./datadbdir:/var/lib/mysql:rw
      - ./scripts/init-database.sql:/docker-entrypoint-initdb.d/init-database.sql
    networks:
      - application-bridge

  ejbca:
    image: keyfactor/ejbca-ce:latest
    depends_on:
      - database
    environment:
      DATABASE_JDBC_URL: *************************************
      LOG_LEVEL_APP: INFO
      LOG_LEVEL_SERVER: INFO
      TLS_SETUP_ENABLED: simple
    ports:
      - "80:8080"
      - "443:8443"
    networks:
      - access-bridge
      - application-bridge
  
  ejbca_license_api:
    build: .
    ports:
      - "5000:80"
    depends_on:
      - postgres
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=ejbca_license;Username=postgres;Password=password
      - Ejbca__BaseUrl=http://ejbca:8080/ejbca/ejbca-rest-api
    volumes:
      - ./appsettings.json:/app/appsettings.json

networks:
  access-bridge:
    driver: bridge
  application-bridge:
    driver: bridge

#volumes:
#  postgres-data: