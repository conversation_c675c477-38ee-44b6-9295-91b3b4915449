-- 建立資料庫 (如果需要)
-- CREATE DATABASE ejbca_license;

-- 客戶授權表
CREATE TABLE IF NOT EXISTS "CustomerLicenses" (
                                                  "Id" UUID PRIMARY KEY,
                                                  "CustomerId" VARCHAR(100) NOT NULL UNIQUE,
    "Total" INTEGER NOT NULL,
    "Used" INTEGER NOT NULL DEFAULT 0,
    "CreatedAt" TIMESTAMP NOT NULL,
    "UpdatedAt" TIMESTAMP NULL
    );

-- Agent 憑證表
CREATE TABLE IF NOT EXISTS "AgentCertificates" (
                                                   "Id" UUID PRIMARY KEY,
                                                   "CustomerId" VARCHAR(100) NOT NULL,
    "AgentId" VARCHAR(100) NOT NULL UNIQUE,
    "CertSerial" VARCHAR(100) NOT NULL UNIQUE,
    "SubjectDn" VARCHAR(500) NOT NULL,
    "IssuedAt" TIMESTAMP NOT NULL,
    "RevokedAt" TIMESTAMP NULL,
    "Status" VARCHAR(20) NOT NULL
    );

-- 憑證審計記錄表
CREATE TABLE IF NOT EXISTS "CertAuditLogs" (
                                               "Id" UUID PRIMARY KEY,
                                               "CertSerial" VARCHAR(100) NULL,
    "Event" VARCHAR(50) NOT NULL,
    "CustomerId" VARCHAR(100) NOT NULL,
    "AgentId" VARCHAR(100) NULL,
    "Message" VARCHAR(1000) NOT NULL,
    "EventTime" TIMESTAMP NOT NULL
    );

-- 建立索引
CREATE INDEX IF NOT EXISTS "IX_AgentCertificates_CustomerId" ON "AgentCertificates" ("CustomerId");
CREATE INDEX IF NOT EXISTS "IX_AgentCertificates_Status" ON "AgentCertificates" ("Status");
CREATE INDEX IF NOT EXISTS "IX_CertAuditLogs_CustomerId" ON "CertAuditLogs" ("CustomerId");
CREATE INDEX IF NOT EXISTS "IX_CertAuditLogs_EventTime" ON "CertAuditLogs" ("EventTime");
CREATE INDEX IF NOT EXISTS "IX_CertAuditLogs_CertSerial" ON "CertAuditLogs" ("CertSerial");

-- 插入測試資料
INSERT INTO "CustomerLicenses" ("Id", "CustomerId", "Total", "Used", "CreatedAt")
VALUES
    (gen_random_uuid(), 'customer001', 100, 0, NOW()),
    (gen_random_uuid(), 'customer002', 50, 0, NOW())
    ON CONFLICT ("CustomerId") DO NOTHING;