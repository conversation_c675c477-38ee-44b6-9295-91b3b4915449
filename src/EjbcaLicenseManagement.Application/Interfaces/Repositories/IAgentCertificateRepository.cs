using EjbcaLicenseManagement.Domain.Entities;

namespace EjbcaLicenseManagement.Application.Interfaces.Repositories;

public interface IAgentCertificateRepository
{
    Task<AgentCertificate?> GetByCertSerialAsync(string certSerial, CancellationToken cancellationToken = default);
    Task<AgentCertificate?> GetByAgentIdAsync(string agentId, CancellationToken cancellationToken = default);
    Task<IEnumerable<AgentCertificate>> GetByCustomerIdAsync(string customerId, CancellationToken cancellationToken = default);
    Task<AgentCertificate> AddAsync(AgentCertificate certificate, CancellationToken cancellationToken = default);
    Task UpdateAsync(AgentCertificate certificate, CancellationToken cancellationToken = default);
}