using EjbcaLicenseManagement.Domain.Entities;

namespace EjbcaLicenseManagement.Application.Interfaces.Repositories;

public interface ICustomerLicenseRepository
{
    Task<CustomerLicense?> GetByCustomerIdAsync(string customerId, CancellationToken cancellationToken = default);
    Task<CustomerLicense> AddAsync(CustomerLicense license, CancellationToken cancellationToken = default);
    Task UpdateAsync(CustomerLicense license, CancellationToken cancellationToken = default);
}