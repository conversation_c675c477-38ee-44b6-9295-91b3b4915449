using EjbcaLicenseManagement.Application.DTOs;
using EjbcaLicenseManagement.Application.Interfaces.Repositories;

namespace EjbcaLicenseManagement.Application.UseCases.Licenses;

public class GetLicenseUsageUseCase(ICustomerLicenseRepository licenseRepository)
{
    public async Task<LicenseUsageResponse> ExecuteAsync(string customerId, CancellationToken cancellationToken = default)
    {
        var license = await licenseRepository.GetByCustomerIdAsync(customerId, cancellationToken);
        if (license == null)
            throw new InvalidOperationException($"Customer {customerId} not found");

        return new LicenseUsageResponse
        {
            CustomerId = license.CustomerId,
            Total = license.Total,
            Used = license.Used,
            Remaining = license.GetRemainingLicenses()
        };
    }
}