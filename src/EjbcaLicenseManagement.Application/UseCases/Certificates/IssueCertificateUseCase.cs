using EjbcaLicenseManagement.Application.DTOs;
using EjbcaLicenseManagement.Application.Interfaces.Repositories;
using EjbcaLicenseManagement.Application.Interfaces.Services;
using EjbcaLicenseManagement.Domain.Entities;

namespace EjbcaLicenseManagement.Application.UseCases.Certificates;

public class IssueCertificateUseCase(
    ICustomerLicenseRepository licenseRepository,
    IAgentCertificateRepository certificateRepository,
    IEjbcaService ejbcaService,
    IAuditService auditService)
{
    public async Task<CertificateResponse> ExecuteAsync(IssueCertificateRequest request, CancellationToken cancellationToken = default)
    {
        // 1. 檢查客戶授權
        var license = await licenseRepository.GetByCustomerIdAsync(request.CustomerId, cancellationToken);
        if (license == null)
            throw new InvalidOperationException($"Customer {request.CustomerId} not found");

        if (!license.CanIssueNewCertificate())
        {
            await auditService.LogAsync(CertAuditLog.CreateQuotaExceeded(request.CustomerId), cancellationToken);
            throw new InvalidOperationException("License quota exceeded");
        }

        // 2. 檢查 Agent 是否已有憑證
        var existingCert = await certificateRepository.GetByAgentIdAsync(request.AgentId, cancellationToken);
        if (existingCert?.IsValid() == true)
            throw new InvalidOperationException($"Agent {request.AgentId} already has a valid certificate");

        try
        {
            // 3. 準備自訂擴展資訊
            var customExtension = $"total={license.Total}";

            // 4. 調用 EJBCA 簽發憑證
            var certSerial = await ejbcaService.IssueCertificateAsync(request.CsrPem, customExtension, cancellationToken);

            // 5. 從 CSR 提取 Subject DN (簡化實作)
            var subjectDn = ExtractSubjectFromCsr(request.CsrPem);

            // 6. 建立憑證記錄
            var certificate = new AgentCertificate(request.CustomerId, request.AgentId, certSerial, subjectDn);
            await certificateRepository.AddAsync(certificate, cancellationToken);

            // 7. 更新授權使用數
            license.UseOneLicense();
            await licenseRepository.UpdateAsync(license, cancellationToken);

            // 8. 記錄審計
            await auditService.LogAsync(CertAuditLog.CreateIssued(request.CustomerId, request.AgentId, certSerial), cancellationToken);

            return new CertificateResponse
            {
                CertSerial = certSerial,
                AgentId = request.AgentId,
                SubjectDn = subjectDn,
                IssuedAt = certificate.IssuedAt,
                Status = certificate.Status.ToString()
            };
        }
        catch (Exception ex)
        {
            await auditService.LogAsync(CertAuditLog.CreateCsrError(request.CustomerId, request.AgentId, ex.Message), cancellationToken);
            throw;
        }
    }

    private string ExtractSubjectFromCsr(string csrPem)
    {
        // 簡化實作，實際應解析 CSR
        return "CN=Agent Certificate";
    }
}