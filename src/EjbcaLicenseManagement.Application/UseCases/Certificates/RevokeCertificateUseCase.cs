using EjbcaLicenseManagement.Application.DTOs;
using EjbcaLicenseManagement.Application.Interfaces.Repositories;
using EjbcaLicenseManagement.Application.Interfaces.Services;
using EjbcaLicenseManagement.Domain.Entities;

namespace EjbcaLicenseManagement.Application.UseCases.Certificates;

public class RevokeCertificateUseCase(
    IAgentCertificateRepository certificateRepository,
    ICustomerLicenseRepository licenseRepository,
    IEjbcaService ejbcaService,
    IAuditService auditService)
{
    public async Task ExecuteAsync(string certSerial, AuditInfo auditInfo, CancellationToken cancellationToken = default)
    {
        // 1. 找到憑證記錄
        var certificate = await certificateRepository.GetByCertSerialAsync(certSerial, cancellationToken);
        if (certificate == null)
            throw new InvalidOperationException("Certificate not found");

        if (!certificate.IsValid())
            throw new InvalidOperationException("Certificate is already revoked");

        // 2. 調用 EJBCA 撤銷憑證
        var revoked = await ejbcaService.RevokeCertificateAsync(certSerial, cancellationToken);
        if (!revoked)
            throw new InvalidOperationException("Failed to revoke certificate in EJBCA");

        // 3. 更新本地憑證狀態
        certificate.Revoke();
        await certificateRepository.UpdateAsync(certificate, cancellationToken);

        // 4. 回收授權額度
        var license = await licenseRepository.GetByCustomerIdAsync(certificate.CustomerId, cancellationToken);
        if (license != null)
        {
            license.ReclaimOneLicense();
            await licenseRepository.UpdateAsync(license, cancellationToken);
        }

        // 5. 記錄審計
        await auditService.LogAsync(
            CertAuditLog.CreateRevoked(certificate.CustomerId, certificate.AgentId, certSerial), 
            cancellationToken);
    }
}