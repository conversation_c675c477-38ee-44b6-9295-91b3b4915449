using EjbcaLicenseManagement.Application.DTOs;
using EjbcaLicenseManagement.Application.Interfaces.Repositories;

namespace EjbcaLicenseManagement.Application.UseCases.Certificates;

public class GetCertificateByAgentUseCase(IAgentCertificateRepository certificateRepository)
{
    public async Task<CertificateResponse> ExecuteAsync(string agentId, CancellationToken cancellationToken = default)
    {
        var certificate = await certificateRepository.GetByAgentIdAsync(agentId, cancellationToken);
        if (certificate == null)
            throw new InvalidOperationException("Certificate not found");

        return new CertificateResponse
        {
            CertSerial = certificate.CertSerial,
            AgentId = certificate.AgentId,
            SubjectDn = certificate.SubjectDn,
            IssuedAt = certificate.IssuedAt,
            Status = certificate.Status.ToString()
        };
    }
}