using EjbcaLicenseManagement.Application.DTOs;
using EjbcaLicenseManagement.Application.Interfaces.Repositories;

namespace EjbcaLicenseManagement.Application.UseCases.Certificates;

public class GetCertificatesUseCase(IAgentCertificateRepository certificateRepository)
{
    public async Task<IEnumerable<CertificateResponse>> ExecuteAsync(string customerId, CancellationToken cancellationToken = default)
    {
        var certificates = await certificateRepository.GetByCustomerIdAsync(customerId, cancellationToken);
        
        return certificates.Select(c => new CertificateResponse
        {
            CertSerial = c.CertSerial,
            AgentId = c.AgentId,
            SubjectDn = c.SubjectDn,
            IssuedAt = c.IssuedAt,
            Status = c.Status.ToString()
        });
    }
}