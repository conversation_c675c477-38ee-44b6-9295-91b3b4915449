using EjbcaLicenseManagement.Api.DTOs.Requests;
using EjbcaLicenseManagement.Api.DTOs.Responses;
using EjbcaLicenseManagement.Application.DTOs;
using EjbcaLicenseManagement.Application.UseCases.Certificates;
using EjbcaLicenseManagement.Application.UseCases.Licenses;
using Microsoft.AspNetCore.Mvc;

namespace EjbcaLicenseManagement.Api.Controllers;

[ApiController]
[Route("api/license")]
[Produces("application/json")]
public class LicenseController(
    IssueCertificateUseCase issueCertificateUseCase,
    RevokeCertificateUseCase revokeCertificateUseCase,
    GetCertificatesUseCase getCertificatesUseCase,
    GetCertificateByAgentUseCase getCertificateByAgentUseCase,
    GetLicenseUsageUseCase getLicenseUsageUseCase)
    : ControllerBase
{
    /// <summary>
    /// 上傳 CSR 並發憑證
    /// </summary>
    [HttpPost("issue-cert")]
    [ProducesResponseType(typeof(LicenseApiResponse<CertificateDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(LicenseApiResponse), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> IssueCertificate([FromBody] IssueCertRequest request)
    {
        try
        {
            var useCaseRequest = new IssueCertificateRequest
            {
                CustomerId = request.CustomerId,
                AgentId = request.AgentId,
                CsrPem = request.CsrPem,
                AuditInfo = GetAuditInfo()
            };

            var result = await issueCertificateUseCase.ExecuteAsync(useCaseRequest);

            var response = new CertificateDto
            {
                CertSerial = result.CertSerial,
                AgentId = result.AgentId,
                SubjectDn = result.SubjectDn,
                IssuedAt = result.IssuedAt,
                Status = result.Status
            };

            return Ok(LicenseApiResponse<CertificateDto>.CreateSuccess(response));
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(LicenseApiResponse.CreateError(ex.Message));
        }
    }

    /// <summary>
    /// 撤銷指定憑證
    /// </summary>
    [HttpPost("revoke-cert")]
    [ProducesResponseType(typeof(LicenseApiResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(LicenseApiResponse), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> RevokeCertificate([FromBody] RevokeCertRequest request)
    {
        try
        {
            await revokeCertificateUseCase.ExecuteAsync(request.CertSerial, GetAuditInfo());
            return Ok(LicenseApiResponse.CreateSuccess("Certificate revoked successfully"));
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(LicenseApiResponse.CreateError(ex.Message));
        }
    }

    /// <summary>
    /// 查詢憑證清單
    /// </summary>
    [HttpGet("certificates")]
    [ProducesResponseType(typeof(LicenseApiResponse<List<CertificateDto>>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetCertificates([FromQuery] string customerId)
    {
        var certificates = await getCertificatesUseCase.ExecuteAsync(customerId);
        
        var response = certificates.Select(c => new CertificateDto
        {
            CertSerial = c.CertSerial,
            AgentId = c.AgentId,
            SubjectDn = c.SubjectDn,
            IssuedAt = c.IssuedAt,
            Status = c.Status
        }).ToList();

        return Ok(LicenseApiResponse<List<CertificateDto>>.CreateSuccess(response));
    }

    /// <summary>
    /// 查單一 agent 憑證
    /// </summary>
    [HttpGet("certificates/{agentId}")]
    [ProducesResponseType(typeof(LicenseApiResponse<CertificateDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(LicenseApiResponse), StatusCodes.Status404NotFound)]
    public async Task<IActionResult> GetCertificateByAgent(string agentId)
    {
        try
        {
            var certificate = await getCertificateByAgentUseCase.ExecuteAsync(agentId);
            
            var response = new CertificateDto
            {
                CertSerial = certificate.CertSerial,
                AgentId = certificate.AgentId,
                SubjectDn = certificate.SubjectDn,
                IssuedAt = certificate.IssuedAt,
                Status = certificate.Status
            };

            return Ok(LicenseApiResponse<CertificateDto>.CreateSuccess(response));
        }
        catch (InvalidOperationException)
        {
            return NotFound(LicenseApiResponse.CreateError("Certificate not found"));
        }
    }

    /// <summary>
    /// 查詢 customer 授權使用狀況
    /// </summary>
    [HttpGet("usage")]
    [ProducesResponseType(typeof(LicenseApiResponse<LicenseUsageDto>), StatusCodes.Status200OK)]
    public async Task<IActionResult> GetLicenseUsage([FromQuery] string customerId)
    {
        try
        {
            var usage = await getLicenseUsageUseCase.ExecuteAsync(customerId);
            
            var response = new LicenseUsageDto
            {
                CustomerId = usage.CustomerId,
                Total = usage.Total,
                Used = usage.Used,
                Remaining = usage.Remaining
            };

            return Ok(LicenseApiResponse<LicenseUsageDto>.CreateSuccess(response));
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(LicenseApiResponse.CreateError(ex.Message));
        }
    }

    private AuditInfo GetAuditInfo()
    {
        return new AuditInfo
        {
            UserId = User.Identity?.Name ?? "anonymous",
            IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "unknown",
            UserAgent = Request.Headers["User-Agent"].ToString()
        };
    }
}
