namespace EjbcaLicenseManagement.Api.DTOs.Responses;

public class LicenseApiResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = null!;
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    public static LicenseApiResponse CreateSuccess(string message = "Success")
    {
        return new LicenseApiResponse { Success = true, Message = message };
    }

    public static LicenseApiResponse CreateError(string message)
    {
        return new LicenseApiResponse { Success = false, Message = message };
    }
}

public class LicenseApiResponse<T> : LicenseApiResponse
{
    public T? Data { get; set; }

    public new static LicenseApiResponse<T> CreateSuccess(T data, string message = "Success")
    {
        return new LicenseApiResponse<T> { Success = true, Message = message, Data = data };
    }

    public new static LicenseApiResponse<T> Error(string message)
    {
        return new LicenseApiResponse<T> { Success = false, Message = message };
    }
}