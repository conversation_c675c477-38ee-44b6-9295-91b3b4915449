<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.3"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.7" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.7" />
        <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.3" />
    </ItemGroup>

    <ItemGroup>
      <Content Include="..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\EjbcaLicenseManagement.Application\EjbcaLicenseManagement.Application.csproj" />
      <ProjectReference Include="..\EjbcaLicenseManagement.Infrastructure\EjbcaLicenseManagement.Infrastructure.csproj" />
    </ItemGroup>

</Project>
