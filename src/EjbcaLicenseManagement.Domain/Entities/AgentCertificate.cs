using EjbcaLicenseManagement.Domain.Enums;

namespace EjbcaLicenseManagement.Domain.Entities;

/// <summary>
/// Agent 憑證實體
/// </summary>
public class AgentCertificate
{
    public Guid Id { get; private set; }
    public string CustomerId { get; private set; }
    public string AgentId { get; private set; }
    public string CertSerial { get; private set; }
    public string SubjectDn { get; private set; }
    public DateTime IssuedAt { get; private set; }
    public DateTime? RevokedAt { get; private set; }
    public CertificateStatus Status { get; private set; }

    private AgentCertificate() 
    { 
        CustomerId = null!;
        AgentId = null!;
        CertSerial = null!;
        SubjectDn = null!;
    } // EF Core

    public AgentCertificate(
        string customerId,
        string agentId,
        string certSerial,
        string subjectDn)
    {
        if (string.IsNullOrWhiteSpace(customerId))
            throw new ArgumentException("Customer ID cannot be empty", nameof(customerId));
        if (string.IsNullOrWhiteSpace(agentId))
            throw new ArgumentException("Agent ID cannot be empty", nameof(agentId));
        if (string.IsNullOrWhiteSpace(certSerial))
            throw new ArgumentException("Certificate serial cannot be empty", nameof(certSerial));
        if (string.IsNullOrWhiteSpace(subjectDn))
            throw new ArgumentException("Subject DN cannot be empty", nameof(subjectDn));

        Id = Guid.NewGuid();
        CustomerId = customerId;
        AgentId = agentId;
        CertSerial = certSerial;
        SubjectDn = subjectDn;
        IssuedAt = DateTime.UtcNow;
        Status = CertificateStatus.Valid;
    }

    /// <summary>
    /// 撤銷憑證
    /// </summary>
    public void Revoke()
    {
        if (Status == CertificateStatus.Revoked)
            throw new InvalidOperationException("Certificate is already revoked");

        Status = CertificateStatus.Revoked;
        RevokedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// 檢查憑證是否有效
    /// </summary>
    public bool IsValid()
    {
        return Status == CertificateStatus.Valid;
    }
}