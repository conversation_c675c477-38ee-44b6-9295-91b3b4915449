namespace EjbcaLicenseManagement.Domain.Entities;

/// <summary>
/// 客戶授權實體
/// </summary>
public class CustomerLicense
{
    public Guid Id { get; private set; }
    public string CustomerId { get; private set; }
    public int Total { get; private set; }
    public int Used { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime? UpdatedAt { get; private set; }

    private CustomerLicense() 
    { 
        CustomerId = null!;
    } // EF Core

    public CustomerLicense(string customerId, int total)
    {
        if (string.IsNullOrWhiteSpace(customerId))
            throw new ArgumentException("Customer ID cannot be empty", nameof(customerId));
        if (total <= 0)
            throw new ArgumentException("Total must be greater than 0", nameof(total));

        Id = Guid.NewGuid();
        CustomerId = customerId;
        Total = total;
        Used = 0;
        CreatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// 檢查是否可以簽發新憑證
    /// </summary>
    public bool CanIssueNewCertificate()
    {
        return Used < Total;
    }

    /// <summary>
    /// 使用一組授權
    /// </summary>
    public void UseOneLicense()
    {
        if (!CanIssueNewCertificate())
            throw new InvalidOperationException("License quota exceeded");

        Used++;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// 回收一組授權（憑證被撤銷時）
    /// </summary>
    public void ReclaimOneLicense()
    {
        if (Used <= 0)
            throw new InvalidOperationException("No license to reclaim");

        Used--;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// 更新授權總數
    /// </summary>
    public void UpdateTotal(int newTotal)
    {
        if (newTotal < Used)
            throw new InvalidOperationException("New total cannot be less than used licenses");

        Total = newTotal;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// 取得剩餘授權數
    /// </summary>
    public int GetRemainingLicenses()
    {
        return Total - Used;
    }
}