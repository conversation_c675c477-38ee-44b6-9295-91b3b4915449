using EjbcaLicenseManagement.Domain.Enums;

namespace EjbcaLicenseManagement.Domain.Entities;

/// <summary>
/// 憑證審計記錄實體
/// </summary>
public class CertAuditLog
{
    public Guid Id { get; private set; }
    public string? CertSerial { get; private set; }
    public AuditEventType Event { get; private set; }
    public string CustomerId { get; private set; }
    public string? AgentId { get; private set; }
    public string Message { get; private set; }
    public DateTime EventTime { get; private set; }

    private CertAuditLog() 
    { 
        CustomerId = null!;
        Message = null!;
    } // EF Core

    public CertAuditLog(
        AuditEventType eventType,
        string customerId,
        string message,
        string? certSerial = null,
        string? agentId = null)
    {
        if (string.IsNullOrWhiteSpace(customerId))
            throw new ArgumentException("Customer ID cannot be empty", nameof(customerId));
        if (string.IsNullOrWhiteSpace(message))
            throw new ArgumentException("Message cannot be empty", nameof(message));

        Id = Guid.NewGuid();
        Event = eventType;
        CustomerId = customerId;
        CertSerial = certSerial;
        AgentId = agentId;
        Message = message;
        EventTime = DateTime.UtcNow;
    }

    /// <summary>
    /// 建立憑證發出審計記錄
    /// </summary>
    public static CertAuditLog CreateIssued(string customerId, string agentId, string certSerial)
    {
        return new CertAuditLog(
            AuditEventType.Issued,
            customerId,
            $"Certificate issued for agent {agentId}",
            certSerial,
            agentId);
    }

    /// <summary>
    /// 建立憑證撤銷審計記錄
    /// </summary>
    public static CertAuditLog CreateRevoked(string customerId, string agentId, string certSerial)
    {
        return new CertAuditLog(
            AuditEventType.Revoked,
            customerId,
            $"Certificate revoked for agent {agentId}",
            certSerial,
            agentId);
    }

    /// <summary>
    /// 建立 CSR 錯誤審計記錄
    /// </summary>
    public static CertAuditLog CreateCsrError(string customerId, string? agentId, string errorMessage)
    {
        return new CertAuditLog(
            AuditEventType.CsrError,
            customerId,
            $"CSR error: {errorMessage}",
            null,
            agentId);
    }

    /// <summary>
    /// 建立配額超額審計記錄
    /// </summary>
    public static CertAuditLog CreateQuotaExceeded(string customerId)
    {
        return new CertAuditLog(
            AuditEventType.QuotaExceeded,
            customerId,
            "License quota exceeded");
    }
}