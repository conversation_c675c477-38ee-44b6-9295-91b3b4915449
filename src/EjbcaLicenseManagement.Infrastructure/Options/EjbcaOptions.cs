namespace EjbcaLicenseManagement.Infrastructure.Options;

public class EjbcaOptions
{
    public const string SectionName = "Ejbca";

    public string BaseUrl { get; set; } = null!;
    public string Username { get; set; } = null!;
    public string Password { get; set; } = null!;
    public string DefaultCA { get; set; } = "ManagementCA";
    public string DefaultCertificateProfile { get; set; } = "ENDUSER";
    public string DefaultEndEntityProfile { get; set; } = "EMPTY";
}