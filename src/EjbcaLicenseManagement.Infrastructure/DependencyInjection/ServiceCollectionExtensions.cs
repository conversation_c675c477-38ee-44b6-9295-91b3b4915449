using EjbcaLicenseManagement.Application.Interfaces.Repositories;
using EjbcaLicenseManagement.Application.Interfaces.Services;
using EjbcaLicenseManagement.Application.UseCases.Certificates;
using EjbcaLicenseManagement.Application.UseCases.Licenses;
using EjbcaLicenseManagement.Infrastructure.Options;
using EjbcaLicenseManagement.Infrastructure.Persistence;
using EjbcaLicenseManagement.Infrastructure.Repositories;
using EjbcaLicenseManagement.Infrastructure.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace EjbcaLicenseManagement.Infrastructure.DependencyInjection;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        // 資料庫
        services.AddDbContext<LicenseDbContext>(options =>
            options.UseNpgsql(configuration.GetConnectionString("DefaultConnection")));

        // 儲存庫
        services.AddScoped<ICustomerLicenseRepository, EfCustomerLicenseRepository>();
        services.AddScoped<IAgentCertificateRepository, EfAgentCertificateRepository>();
        services.AddScoped<ICertAuditLogRepository, EfCertAuditLogRepository>();

        // 服務
        services.AddScoped<IAuditService, AuditService>();
        services.AddHttpClient<IEjbcaService, EjbcaService>();

        // 選項
        services.Configure<EjbcaOptions>(configuration.GetSection(EjbcaOptions.SectionName));

        // Use Cases
        services.AddScoped<IssueCertificateUseCase>();
        services.AddScoped<RevokeCertificateUseCase>();
        services.AddScoped<GetCertificatesUseCase>();
        services.AddScoped<GetCertificateByAgentUseCase>();
        services.AddScoped<GetLicenseUsageUseCase>();

        return services;
    }
}