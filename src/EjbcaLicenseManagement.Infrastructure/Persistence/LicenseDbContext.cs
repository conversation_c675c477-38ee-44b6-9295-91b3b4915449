using EjbcaLicenseManagement.Domain.Entities;
using EjbcaLicenseManagement.Infrastructure.Persistence.Configurations;
using Microsoft.EntityFrameworkCore;

namespace EjbcaLicenseManagement.Infrastructure.Persistence;

public class LicenseDbContext(DbContextOptions<LicenseDbContext> options) : DbContext(options)
{
    public DbSet<CustomerLicense> CustomerLicenses { get; set; }
    public DbSet<AgentCertificate> AgentCertificates { get; set; }
    public DbSet<CertAuditLog> CertAuditLogs { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.ApplyConfiguration(new CustomerLicenseConfiguration());
        modelBuilder.ApplyConfiguration(new AgentCertificateConfiguration());
        modelBuilder.ApplyConfiguration(new CertAuditLogConfiguration());
    }
}