using EjbcaLicenseManagement.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EjbcaLicenseManagement.Infrastructure.Persistence.Configurations;

public class CustomerLicenseConfiguration : IEntityTypeConfiguration<CustomerLicense>
{
    public void Configure(EntityTypeBuilder<CustomerLicense> builder)
    {
        builder.ToTable("CustomerLicenses");

        builder.HasKey(x => x.Id);

        builder.Property(x => x.CustomerId)
            .IsRequired()
            .HasMaxLength(100);

        builder.HasIndex(x => x.CustomerId)
            .IsUnique();

        builder.Property(x => x.Total)
            .IsRequired();

        builder.Property(x => x.Used)
            .IsRequired();

        builder.Property(x => x.CreatedAt)
            .IsRequired();

        builder.Property(x => x.UpdatedAt);
    }
}