using EjbcaLicenseManagement.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EjbcaLicenseManagement.Infrastructure.Persistence.Configurations;

public class AgentCertificateConfiguration : IEntityTypeConfiguration<AgentCertificate>
{
    public void Configure(EntityTypeBuilder<AgentCertificate> builder)
    {
        builder.ToTable("AgentCertificates");

        builder.Has<PERSON>ey(x => x.Id);

        builder.Property(x => x.CustomerId)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(x => x.AgentId)
            .IsRequired()
            .HasMaxLength(100);

        builder.HasIndex(x => x.AgentId)
            .IsUnique();

        builder.Property(x => x.CertSerial)
            .IsRequired()
            .HasMaxLength(100);

        builder.HasIndex(x => x.CertSerial)
            .IsUnique();

        builder.Property(x => x.SubjectDn)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(x => x.Status)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(x => x.IssuedAt)
            .IsRequired();

        builder.Property(x => x.RevokedAt);

        builder.HasIndex(x => x.CustomerId);
        builder.HasIndex(x => x.Status);
    }
}