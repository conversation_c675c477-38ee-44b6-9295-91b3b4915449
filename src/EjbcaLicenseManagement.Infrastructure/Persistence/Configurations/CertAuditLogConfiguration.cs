using EjbcaLicenseManagement.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace EjbcaLicenseManagement.Infrastructure.Persistence.Configurations;

public class CertAuditLogConfiguration : IEntityTypeConfiguration<CertAuditLog>
{
    public void Configure(EntityTypeBuilder<CertAuditLog> builder)
    {
        builder.ToTable("CertAuditLogs");

        builder.HasKey(x => x.Id);

        builder.Property(x => x.CertSerial)
            .HasMaxLength(100);

        builder.Property(x => x.Event)
            .IsRequired()
            .HasConversion<string>();

        builder.Property(x => x.CustomerId)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(x => x.AgentId)
            .HasMaxLength(100);

        builder.Property(x => x.Message)
            .IsRequired()
            .HasMaxLength(1000);

        builder.Property(x => x.EventTime)
            .IsRequired();

        builder.HasIndex(x => x.CustomerId);
        builder.HasIndex(x => x.EventTime);
        builder.HasIndex(x => x.CertSerial);
    }
}