using System.Text;
using System.Text.Json;
using EjbcaLicenseManagement.Application.Interfaces.Services;
using EjbcaLicenseManagement.Infrastructure.Options;
using Microsoft.Extensions.Options;

namespace EjbcaLicenseManagement.Infrastructure.Services;

public class EjbcaService(HttpClient httpClient, IOptions<EjbcaOptions> options) : IEjbcaService
{
    private readonly EjbcaOptions _options = options.Value;

    public async Task<string> IssueCertificateAsync(string csrPem, string customExtension, CancellationToken cancellationToken = default)
    {
        var request = new
        {
            certificate_request = csrPem,
            certificate_profile_name = _options.DefaultCertificateProfile,
            end_entity_profile_name = _options.DefaultEndEntityProfile,
            certificate_authority_name = _options.DefaultCA,
            username = Guid.NewGuid().ToString(), // 動態產生 username
            password = "temp123", // 臨時密碼
            include_chain = false,
            // 自訂擴展 (需要根據 EJBCA API 實際格式調整)
            extensions = new[]
            {
                new
                {
                    oid = "1.3.6.1.4.1.9999.1.1",
                    value = customExtension,
                    critical = false
                }
            }
        };

        var json = JsonSerializer.Serialize(request);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        // 加入基本認證
        var authString = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{_options.Username}:{_options.Password}"));
        httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", authString);

        var response = await httpClient.PostAsync($"{_options.BaseUrl}/v1/certificate/pkcs10enroll", content, cancellationToken);
        
        if (!response.IsSuccessStatusCode)
        {
            var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
            throw new InvalidOperationException($"EJBCA API error: {response.StatusCode} - {errorContent}");
        }

        var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
        var responseData = JsonSerializer.Deserialize<JsonElement>(responseContent);
        
        // 從回應中提取憑證序號 (需要根據實際 EJBCA API 回應格式調整)
        if (responseData.TryGetProperty("serial_number", out var serialElement))
        {
            return serialElement.GetString() ?? throw new InvalidOperationException("Invalid certificate serial number");
        }

        throw new InvalidOperationException("Certificate serial number not found in response");
    }

    public async Task<bool> RevokeCertificateAsync(string certSerial, CancellationToken cancellationToken = default)
    {
        var request = new
        {
            certificate_serial_number = certSerial,
            reason_code = 1, // Key compromise
            delete = false
        };

        var json = JsonSerializer.Serialize(request);
        var content = new StringContent(json, Encoding.UTF8, "application/json");

        // 加入基本認證
        var authString = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{_options.Username}:{_options.Password}"));
        httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", authString);

        var response = await httpClient.PostAsync($"{_options.BaseUrl}/v1/certificate/revoke", content, cancellationToken);
        
        return response.IsSuccessStatusCode;
    }
}