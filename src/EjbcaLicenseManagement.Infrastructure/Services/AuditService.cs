using EjbcaLicenseManagement.Application.Interfaces.Repositories;
using EjbcaLicenseManagement.Application.Interfaces.Services;
using EjbcaLicenseManagement.Domain.Entities;

namespace EjbcaLicenseManagement.Infrastructure.Services;

public class AuditService(ICertAuditLogRepository auditRepository) : IAuditService
{
    public async Task LogAsync(CertAuditLog auditLog, CancellationToken cancellationToken = default)
    {
        await auditRepository.AddAsync(auditLog, cancellationToken);
    }
}