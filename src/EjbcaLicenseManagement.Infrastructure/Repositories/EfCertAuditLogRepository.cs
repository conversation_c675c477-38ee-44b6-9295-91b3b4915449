using EjbcaLicenseManagement.Application.Interfaces.Repositories;
using EjbcaLicenseManagement.Domain.Entities;
using EjbcaLicenseManagement.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace EjbcaLicenseManagement.Infrastructure.Repositories;

public class EfCertAuditLogRepository(LicenseDbContext context) : ICertAuditLogRepository
{
    public async Task<CertAuditLog> AddAsync(CertAuditLog auditLog, CancellationToken cancellationToken = default)
    {
        context.CertAuditLogs.Add(auditLog);
        await context.SaveChangesAsync(cancellationToken);
        return auditLog;
    }

    public async Task<IEnumerable<CertAuditLog>> GetByCustomerIdAsync(string customerId, CancellationToken cancellationToken = default)
    {
        return await context.CertAuditLogs
            .Where(x => x.CustomerId == customerId)
            .OrderByDescending(x => x.EventTime)
            .ToListAsync(cancellationToken);
    }
}