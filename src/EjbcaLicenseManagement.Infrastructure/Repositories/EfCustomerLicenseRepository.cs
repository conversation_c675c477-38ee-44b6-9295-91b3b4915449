using EjbcaLicenseManagement.Application.Interfaces.Repositories;
using EjbcaLicenseManagement.Domain.Entities;
using EjbcaLicenseManagement.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace EjbcaLicenseManagement.Infrastructure.Repositories;

public class EfCustomerLicenseRepository(LicenseDbContext context) : ICustomerLicenseRepository
{
    public async Task<CustomerLicense?> GetByCustomerIdAsync(string customerId, CancellationToken cancellationToken = default)
    {
        return await context.CustomerLicenses
            .FirstOrDefaultAsync(x => x.CustomerId == customerId, cancellationToken);
    }

    public async Task<CustomerLicense> AddAsync(CustomerLicense license, CancellationToken cancellationToken = default)
    {
        context.CustomerLicenses.Add(license);
        await context.SaveChangesAsync(cancellationToken);
        return license;
    }

    public async Task UpdateAsync(CustomerLicense license, CancellationToken cancellationToken = default)
    {
        context.CustomerLicenses.Update(license);
        await context.SaveChangesAsync(cancellationToken);
    }
}
