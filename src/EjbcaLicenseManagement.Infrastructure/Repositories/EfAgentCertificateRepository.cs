using EjbcaLicenseManagement.Application.Interfaces.Repositories;
using EjbcaLicenseManagement.Domain.Entities;
using EjbcaLicenseManagement.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace EjbcaLicenseManagement.Infrastructure.Repositories;

public class EfAgentCertificateRepository(LicenseDbContext context) : IAgentCertificateRepository
{
    public async Task<AgentCertificate?> GetByCertSerialAsync(string certSerial, CancellationToken cancellationToken = default)
    {
        return await context.AgentCertificates
            .FirstOrDefaultAsync(x => x.CertSerial == certSerial, cancellationToken);
    }

    public async Task<AgentCertificate?> GetByAgentIdAsync(string agentId, CancellationToken cancellationToken = default)
    {
        return await context.AgentCertificates
            .FirstOrDefaultAsync(x => x.AgentId == agentId, cancellationToken);
    }

    public async Task<IEnumerable<AgentCertificate>> GetByCustomerIdAsync(string customerId, CancellationToken cancellationToken = default)
    {
        return await context.AgentCertificates
            .Where(x => x.CustomerId == customerId)
            .OrderByDescending(x => x.IssuedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<AgentCertificate> AddAsync(AgentCertificate certificate, CancellationToken cancellationToken = default)
    {
        context.AgentCertificates.Add(certificate);
        await context.SaveChangesAsync(cancellationToken);
        return certificate;
    }

    public async Task UpdateAsync(AgentCertificate certificate, CancellationToken cancellationToken = default)
    {
        context.AgentCertificates.Update(certificate);
        await context.SaveChangesAsync(cancellationToken);
    }
}