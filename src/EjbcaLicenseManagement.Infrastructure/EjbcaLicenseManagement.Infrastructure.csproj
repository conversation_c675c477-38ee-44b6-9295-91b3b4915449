﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.7" />
      <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.7" />
      <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.7" />
      <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\EjbcaLicenseManagement.Application\EjbcaLicenseManagement.Application.csproj" />
      <ProjectReference Include="..\EjbcaLicenseManagement.Domain\EjbcaLicenseManagement.Domain.csproj" />
    </ItemGroup>

</Project>
